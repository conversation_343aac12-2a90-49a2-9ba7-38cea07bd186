2025-08-03 10:00:01 [INFO] Application started successfully
2025-08-03 10:00:02 [INFO] Loading configuration from config.yaml
2025-08-03 10:00:03 [INFO] Database connection established
2025-08-03 10:00:04 [WARN] High memory usage detected: 85%
2025-08-03 10:00:05 [INFO] HTTP server listening on port 8080
2025-08-03 10:00:06 [ERROR] Failed to connect to Redis: connection refused
2025-08-03 10:00:07 [WARN] Retrying Redis connection in 5 seconds
2025-08-03 10:00:08 [INFO] Redis connection established
2025-08-03 10:00:09 [DEBUG] Processing user request: GET /api/users
2025-08-03 10:00:10 [INFO] Request completed: 200 OK
