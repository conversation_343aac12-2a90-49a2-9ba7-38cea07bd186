package ui

import (
	"fmt"
	"logtui/internal/tailer"
	"os"
	"path/filepath"
	"time"

	"github.com/gdamore/tcell/v2"
	"github.com/rivo/tview"
)

// App 主应用程序结构
type App struct {
	*tview.Application

	// UI组件
	fileList  *FileList
	logView   *LogView
	statusBar *StatusBar
	helpBar   *tview.TextView

	// 布局
	mainFlex   *tview.Flex
	leftPanel  *tview.Flex
	rightPanel *tview.Flex

	// 应用状态
	currentPath   string
	searchMode    bool
	searchInput   *tview.InputField
	tailerManager *tailer.TailerManager
}

// NewApp 创建新的应用程序实例
func NewApp() *App {
	app := &App{
		Application:   tview.NewApplication(),
		tailerManager: tailer.NewTailerManager(),
	}

	app.initComponents()
	app.setupLayout()
	app.setupKeyBindings()
	app.startLogWatcher()

	// 设置初始焦点到文件列表
	app.SetFocus(app.fileList)

	return app
}

// initComponents 初始化UI组件
func (app *App) initComponents() {
	// 创建主要组件
	app.fileList = NewFileList()
	app.logView = NewLogView()
	app.statusBar = NewStatusBar()

	// 创建帮助栏
	app.helpBar = tview.NewTextView().
		SetDynamicColors(true).
		SetText(app.statusBar.GetHelpText())

	// 创建搜索输入框（初始隐藏）
	app.searchInput = tview.NewInputField().
		SetLabel("搜索: ").
		SetFieldWidth(30)

	// 设置文件选择回调
	app.fileList.SetOnSelect(func(filename string) {
		app.onFileSelected(filename)
	})
}

// setupLayout 设置布局
func (app *App) setupLayout() {
	// 左侧面板（文件列表）
	app.leftPanel = tview.NewFlex().SetDirection(tview.FlexRow).
		AddItem(app.fileList, 0, 1, true)

	// 右侧面板（日志视图）
	app.rightPanel = tview.NewFlex().SetDirection(tview.FlexRow).
		AddItem(app.logView, 0, 1, false)

	// 主布局（左右分栏）
	contentFlex := tview.NewFlex().SetDirection(tview.FlexColumn).
		AddItem(app.leftPanel, 0, 1, true).  // 左侧文件列表，权重1
		AddItem(app.rightPanel, 0, 3, false) // 右侧日志视图，权重3

	// 整体布局（上中下）
	app.mainFlex = tview.NewFlex().SetDirection(tview.FlexRow).
		AddItem(contentFlex, 0, 1, true).    // 主内容区域
		AddItem(app.statusBar, 3, 0, false). // 状态栏，固定高度3
		AddItem(app.helpBar, 1, 0, false)    // 帮助栏，固定高度1

	app.SetRoot(app.mainFlex, true)
}

// setupKeyBindings 设置快捷键绑定
func (app *App) setupKeyBindings() {
	app.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
		// 如果在搜索模式，让搜索输入框处理按键
		if app.searchMode {
			return app.handleSearchInput(event)
		}

		switch event.Key() {
		case tcell.KeyRune:
			switch event.Rune() {
			case 'q', 'Q':
				// 退出程序
				app.Stop()
				return nil
			case 's', 'S':
				// 进入搜索模式
				app.enterSearchMode()
				return nil
			case 'p', 'P':
				// 切换暂停状态
				app.togglePause()
				return nil
			case 'c', 'C':
				// 清空日志
				app.clearLog()
				return nil
			case 'r', 'R':
				// 刷新文件列表
				app.refreshFileList()
				return nil
			case 'i', 'I':
				// 显示当前文件信息
				app.showFileInfo()
				return nil
			case '?':
				// 显示帮助信息
				app.showHelp()
				return nil
			case 'n', 'N':
				// 下一个搜索匹配项
				if app.logView.NextMatch() {
					app.updateSearchStatus()
				}
				return nil
			case 'b', 'B':
				// 上一个搜索匹配项
				if app.logView.PrevMatch() {
					app.updateSearchStatus()
				}
				return nil
			}
		case tcell.KeyEscape:
			// 退出搜索模式
			if app.searchMode {
				app.exitSearchMode()
				return nil
			}
		}

		return event
	})
}

// handleSearchInput 处理搜索输入
func (app *App) handleSearchInput(event *tcell.EventKey) *tcell.EventKey {
	switch event.Key() {
	case tcell.KeyEnter:
		// 执行搜索
		searchTerm := app.searchInput.GetText()
		app.performSearch(searchTerm)
		app.exitSearchMode()
		return nil
	case tcell.KeyTab:
		// 下一个匹配项
		if app.logView.NextMatch() {
			app.updateSearchStatus()
		}
		return nil
	case tcell.KeyEscape:
		// 取消搜索
		app.exitSearchMode()
		return nil
	}

	return event
}

// onFileSelected 文件选择回调
func (app *App) onFileSelected(filename string) {
	app.statusBar.SetCurrentFile(filepath.Base(filename))

	// 更新文件列表的选中状态
	app.fileList.SetSelectedFile(filename)

	// 清空当前日志显示
	app.logView.Clear()

	// 加载文件的最后50行作为历史
	if err := app.logView.LoadFileHistory(filename, 50); err != nil {
		app.statusBar.ShowError(fmt.Errorf("加载文件历史失败: %v", err))
		app.logView.AddLine(fmt.Sprintf("⚠️ 警告: 无法加载文件历史 %s", filename))
		app.logView.AddLine(fmt.Sprintf("原因: %v", err))
	} else {
		// 显示加载的历史行数
		historyCount := app.logView.GetLineCount()
		if historyCount > 0 {
			app.logView.AddLine(fmt.Sprintf("📚 已加载 %d 行历史记录", historyCount))
		}
	}

	// 切换到新文件的监听器
	tailer, err := app.tailerManager.SwitchFile(filename)
	if err != nil {
		app.statusBar.ShowError(fmt.Errorf("启动文件监听失败: %v", err))
		app.logView.AddLine(fmt.Sprintf("❌ 错误: 无法监听文件 %s", filename))
		app.logView.AddLine(fmt.Sprintf("原因: %v", err))
		return
	}

	app.statusBar.ShowSuccess(fmt.Sprintf("🎯 开始监听: %s", filepath.Base(filename)))
	app.logView.AddLine(fmt.Sprintf("🔄 --- 开始实时监听 %s ---", filepath.Base(filename)))

	// 更新状态栏行数
	app.statusBar.SetLineCount(app.logView.GetLineCount())

	// 启动日志监听goroutine
	go app.watchLogOutput(tailer)
}

// enterSearchMode 进入搜索模式
func (app *App) enterSearchMode() {
	app.searchMode = true
	app.searchInput.SetText("")

	// 替换帮助栏为搜索输入框
	app.mainFlex.RemoveItem(app.helpBar)
	app.mainFlex.AddItem(app.searchInput, 1, 0, true)

	// 更新状态栏显示搜索帮助
	app.statusBar.ShowMessage("🔍 输入搜索关键词，按回车确认，Tab导航，Esc取消", 0)

	app.SetFocus(app.searchInput)
}

// exitSearchMode 退出搜索模式
func (app *App) exitSearchMode() {
	app.searchMode = false

	// 恢复帮助栏
	app.mainFlex.RemoveItem(app.searchInput)
	app.mainFlex.AddItem(app.helpBar, 1, 0, false)

	// 恢复正常状态显示
	app.statusBar.updateDisplay()

	app.SetFocus(app.fileList)
}

// performSearch 执行搜索
func (app *App) performSearch(term string) {
	if err := app.logView.SetSearchTerm(term); err != nil {
		app.statusBar.ShowError(err)
		return
	}

	app.statusBar.SetSearchTerm(term)
	if term != "" {
		app.updateSearchStatus()
	} else {
		app.statusBar.ShowSuccess("✨ 已清除搜索")
	}
}

// updateSearchStatus 更新搜索状态显示
func (app *App) updateSearchStatus() {
	current, total, term := app.logView.GetSearchInfo()
	if total == 0 {
		app.statusBar.ShowMessage(fmt.Sprintf("🔍 '%s': 无匹配结果", term), 2*time.Second)
	} else {
		app.statusBar.ShowMessage(fmt.Sprintf("🔍 '%s': %d/%d (n下一个 b上一个)", term, current, total), 3*time.Second)
	}
}

// togglePause 切换暂停状态
func (app *App) togglePause() {
	currentlyPaused := !app.logView.IsAutoScrollEnabled()
	newPausedState := !currentlyPaused

	app.logView.SetAutoScroll(!newPausedState)
	app.statusBar.SetPaused(newPausedState)

	if newPausedState {
		app.statusBar.ShowMessage("⏸️ 已暂停自动滚动 - 按 'p' 恢复", 2*time.Second)
		// 更新日志视图标题显示暂停状态
		app.logView.SetTitle(" 日志内容 (已暂停) ")
	} else {
		app.statusBar.ShowMessage("▶️ 已恢复自动滚动", 2*time.Second)
		// 恢复正常标题
		app.logView.SetTitle(" 日志内容 ")
		// 滚动到底部
		app.logView.ScrollToEnd()
	}
}

// clearLog 清空日志
func (app *App) clearLog() {
	app.logView.Clear()
	app.statusBar.SetLineCount(0)
	app.statusBar.ShowSuccess("已清空日志")
}

// refreshFileList 刷新文件列表
func (app *App) refreshFileList() {
	if err := app.fileList.Refresh(); err != nil {
		app.statusBar.ShowError(err)
	} else {
		app.statusBar.ShowSuccess("已刷新文件列表")
	}
}

// showScrollStatus 显示当前滚动状态
func (app *App) showScrollStatus() {
	if app.logView.IsAutoScrollEnabled() {
		app.statusBar.ShowMessage("📜 自动滚动: 开启", 1*time.Second)
	} else {
		app.statusBar.ShowMessage("📜 自动滚动: 已暂停 - 按 'p' 恢复", 2*time.Second)
	}
}

// showFileInfo 显示当前文件信息
func (app *App) showFileInfo() {
	currentFile := app.fileList.GetCurrentFile()
	if currentFile == "" {
		app.statusBar.ShowMessage("ℹ️ 未选择文件", 2*time.Second)
		return
	}

	// 获取文件信息
	if stat, err := os.Stat(currentFile); err == nil {
		info := fmt.Sprintf("📁 %s | 📏 %s | 🕒 %s",
			filepath.Base(currentFile),
			app.formatFileSize(stat.Size()),
			stat.ModTime().Format("2006-01-02 15:04:05"))
		app.statusBar.ShowMessage(info, 3*time.Second)
	} else {
		app.statusBar.ShowError(fmt.Errorf("无法获取文件信息: %v", err))
	}
}

// showHelp 显示帮助信息
func (app *App) showHelp() {
	current, total, searchTerm := app.logView.GetSearchInfo()
	var helpText string

	if searchTerm != "" && total > 0 {
		// 搜索模式下的帮助
		helpText = fmt.Sprintf("🔍 搜索'%s'(%d/%d): n下一个 b上一个 s新搜索 p暂停 c清空 q退出", searchTerm, current, total)
	} else if searchTerm != "" {
		// 搜索无结果
		helpText = "🔍 无搜索结果: s新搜索 c清空 ↑↓切换文件 q退出"
	} else {
		// 普通模式帮助
		helpText = "🔧 LogTUI: ↑↓选择 ⏎确认 s搜索 p暂停 c清空 r刷新 i信息 ?帮助 q退出"
	}

	app.statusBar.ShowMessage(helpText, 4*time.Second)
}

// formatFileSize 格式化文件大小
func (app *App) formatFileSize(size int64) string {
	const unit = 1024
	if size < unit {
		return fmt.Sprintf("%d B", size)
	}
	div, exp := int64(unit), 0
	for n := size / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(size)/float64(div), "KMGTPE"[exp])
}

// SetLogPath 设置日志路径
func (app *App) SetLogPath(path string) error {
	// 检查路径是否存在
	if _, err := os.Stat(path); os.IsNotExist(err) {
		return fmt.Errorf("路径不存在: %s", path)
	}

	app.currentPath = path

	// 加载文件列表
	if err := app.fileList.SetPath(path); err != nil {
		return fmt.Errorf("加载文件列表失败: %v", err)
	}

	app.statusBar.ShowSuccess(fmt.Sprintf("已设置路径: %s", path))
	return nil
}

// startLogWatcher 启动日志监听器
func (app *App) startLogWatcher() {
	// 这个方法在应用启动时调用，用于初始化监听相关的资源
	// 实际的监听会在文件选择时启动
}

// watchLogOutput 监听日志输出
func (app *App) watchLogOutput(t *tailer.Tailer) {
	outputChan := t.GetOutputChan()
	errorChan := t.GetErrorChan()

	for {
		select {
		case logLine, ok := <-outputChan:
			if !ok {
				// channel已关闭，监听器已停止
				return
			}

			// 在UI线程中更新日志显示
			app.QueueUpdateDraw(func() {
				app.logView.AddLine(logLine.Text)
				app.statusBar.SetLineCount(app.logView.GetLineCount())
			})

		case err, ok := <-errorChan:
			if !ok {
				// 错误channel已关闭
				return
			}

			// 在UI线程中显示错误
			app.QueueUpdateDraw(func() {
				app.statusBar.ShowError(err)
				app.logView.AddLine(fmt.Sprintf("监听错误: %v", err))
			})
		}
	}
}

// Run 运行应用程序
func (app *App) Run() error {
	// 确保在退出时清理资源
	defer app.tailerManager.Cleanup()
	return app.Application.Run()
}
