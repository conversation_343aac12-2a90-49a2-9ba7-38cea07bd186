package ui

import (
	"bufio"
	"fmt"
	"os"
	"regexp"
	"strings"
	"sync"

	"github.com/rivo/tview"
)

// LogLevel 日志级别
type LogLevel int

const (
	LogLevelInfo LogLevel = iota
	LogLevelWarn
	LogLevelError
	LogLevelDebug
	LogLevelUnknown
)

// LogView 日志展示组件
type LogView struct {
	*tview.TextView
	mutex        sync.RWMutex
	lines        []string
	maxLines     int
	autoScroll   bool
	searchTerm   string
	highlightReg *regexp.Regexp
}

// NewLogView 创建新的日志展示组件
func NewLogView() *LogView {
	textView := tview.NewTextView()

	lv := &LogView{
		TextView:   textView,
		lines:      make([]string, 0),
		maxLines:   1000, // 最大保留1000行
		autoScroll: true,
	}

	// 设置样式
	textView.SetBorder(true).
		SetTitle(" 日志内容 ").
		SetTitleAlign(tview.AlignLeft)
	textView.SetScrollable(true).
		SetWrap(true).
		SetDynamicColors(true)

	return lv
}

// AddLine 添加新的日志行
func (lv *LogView) AddLine(line string) {
	lv.mutex.Lock()
	defer lv.mutex.Unlock()

	// 添加新行
	lv.lines = append(lv.lines, line)

	// 限制最大行数，删除旧行
	if len(lv.lines) > lv.maxLines {
		lv.lines = lv.lines[len(lv.lines)-lv.maxLines:]
	}

	// 更新显示
	lv.updateDisplay()
}

// updateDisplay 更新显示内容
func (lv *LogView) updateDisplay() {
	var content strings.Builder

	for _, line := range lv.lines {
		processedLine := lv.processLine(line)
		content.WriteString(processedLine)
		content.WriteString("\n")
	}

	lv.SetText(content.String())

	// 自动滚动到底部
	if lv.autoScroll {
		lv.ScrollToEnd()
	}
}

// processLine 处理单行日志（着色和高亮）
func (lv *LogView) processLine(line string) string {
	// 首先应用日志级别着色
	coloredLine := lv.applyLogLevelColor(line)

	// 然后应用搜索高亮
	if lv.highlightReg != nil {
		coloredLine = lv.applySearchHighlight(coloredLine)
	}

	return coloredLine
}

// applyLogLevelColor 应用日志级别颜色
func (lv *LogView) applyLogLevelColor(line string) string {
	level := lv.detectLogLevel(line)

	switch level {
	case LogLevelError:
		return fmt.Sprintf("[red]%s[white]", line)
	case LogLevelWarn:
		return fmt.Sprintf("[yellow]%s[white]", line)
	case LogLevelInfo:
		return fmt.Sprintf("[green]%s[white]", line)
	case LogLevelDebug:
		return fmt.Sprintf("[blue]%s[white]", line)
	default:
		return line
	}
}

// detectLogLevel 检测日志级别
func (lv *LogView) detectLogLevel(line string) LogLevel {
	upperLine := strings.ToUpper(line)

	if strings.Contains(upperLine, "ERROR") || strings.Contains(upperLine, "FATAL") {
		return LogLevelError
	}
	if strings.Contains(upperLine, "WARN") || strings.Contains(upperLine, "WARNING") {
		return LogLevelWarn
	}
	if strings.Contains(upperLine, "INFO") {
		return LogLevelInfo
	}
	if strings.Contains(upperLine, "DEBUG") || strings.Contains(upperLine, "TRACE") {
		return LogLevelDebug
	}

	return LogLevelUnknown
}

// applySearchHighlight 应用搜索高亮
func (lv *LogView) applySearchHighlight(line string) string {
	if lv.highlightReg == nil {
		return line
	}

	// 使用黄色背景高亮匹配的文本
	return lv.highlightReg.ReplaceAllString(line, "[black:yellow]$0[white:-]")
}

// SetSearchTerm 设置搜索关键词
func (lv *LogView) SetSearchTerm(term string) error {
	lv.mutex.Lock()
	defer lv.mutex.Unlock()

	lv.searchTerm = term

	if term == "" {
		lv.highlightReg = nil
	} else {
		// 创建不区分大小写的正则表达式
		reg, err := regexp.Compile("(?i)" + regexp.QuoteMeta(term))
		if err != nil {
			return fmt.Errorf("无效的搜索词: %v", err)
		}
		lv.highlightReg = reg
	}

	// 重新渲染所有行
	lv.updateDisplay()
	return nil
}

// Clear 清空日志内容
func (lv *LogView) Clear() {
	lv.mutex.Lock()
	defer lv.mutex.Unlock()

	lv.lines = lv.lines[:0]
	lv.SetText("")
}

// SetAutoScroll 设置是否自动滚动
func (lv *LogView) SetAutoScroll(enabled bool) {
	lv.mutex.Lock()
	defer lv.mutex.Unlock()

	lv.autoScroll = enabled

	// 更新标题显示滚动状态
	title := " 日志内容 "
	if !enabled {
		title += "(已暂停) "
	}
	lv.SetTitle(title)
}

// IsAutoScrollEnabled 检查是否启用自动滚动
func (lv *LogView) IsAutoScrollEnabled() bool {
	lv.mutex.RLock()
	defer lv.mutex.RUnlock()
	return lv.autoScroll
}

// GetLineCount 获取当前行数
func (lv *LogView) GetLineCount() int {
	lv.mutex.RLock()
	defer lv.mutex.RUnlock()
	return len(lv.lines)
}

// SetMaxLines 设置最大保留行数
func (lv *LogView) SetMaxLines(maxLines int) {
	lv.mutex.Lock()
	defer lv.mutex.Unlock()

	lv.maxLines = maxLines

	// 如果当前行数超过限制，删除旧行
	if len(lv.lines) > lv.maxLines {
		lv.lines = lv.lines[len(lv.lines)-lv.maxLines:]
		lv.updateDisplay()
	}
}

// LoadFileHistory 加载文件的最后几行作为历史
func (lv *LogView) LoadFileHistory(filename string, lines int) error {
	lv.mutex.Lock()
	defer lv.mutex.Unlock()

	file, err := os.Open(filename)
	if err != nil {
		return fmt.Errorf("打开文件失败: %v", err)
	}
	defer file.Close()

	// 读取文件的所有行
	var fileLines []string
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		fileLines = append(fileLines, scanner.Text())
	}

	if err := scanner.Err(); err != nil {
		return fmt.Errorf("读取文件失败: %v", err)
	}

	// 获取最后几行
	start := 0
	if len(fileLines) > lines {
		start = len(fileLines) - lines
	}

	// 清空当前内容并添加历史行
	lv.lines = lv.lines[:0]
	for i := start; i < len(fileLines); i++ {
		lv.lines = append(lv.lines, fileLines[i])
	}

	// 更新显示
	lv.updateDisplay()
	return nil
}
