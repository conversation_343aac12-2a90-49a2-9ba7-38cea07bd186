package ui

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/rivo/tview"
)

// FileList 文件列表组件
type FileList struct {
	*tview.List
	files         []string
	currentPath   string
	selectedIndex int
	onSelect      func(string)
}

// NewFileList 创建新的文件列表组件
func NewFileList() *FileList {
	list := tview.NewList()

	fl := &FileList{
		List:          list,
		files:         make([]string, 0),
		selectedIndex: -1,
	}

	// 设置样式
	list.SetBorder(true).
		SetTitle(" 日志文件 ").
		SetTitleAlign(tview.AlignLeft)

	// 设置选择回调（Enter键）
	list.SetSelectedFunc(func(index int, mainText, secondaryText string, shortcut rune) {
		if fl.onSelect != nil && index < len(fl.files) {
			fl.onSelect(fl.files[index])
		}
	})

	// 设置焦点变化回调（光标移动时自动加载）
	list.SetChangedFunc(func(index int, mainText, secondaryText string, shortcut rune) {
		if fl.onSelect != nil && index < len(fl.files) {
			fl.selectedIndex = index
			fl.onSelect(fl.files[index])
		}
	})

	return fl
}

// SetPath 设置监听路径并加载文件列表
func (fl *FileList) SetPath(path string) error {
	fl.currentPath = path
	return fl.loadFiles()
}

// loadFiles 加载指定路径下的日志文件
func (fl *FileList) loadFiles() error {
	fl.Clear()
	fl.files = fl.files[:0]

	if fl.currentPath == "" {
		return fmt.Errorf("路径未设置")
	}

	// 检查路径是否存在
	if _, err := os.Stat(fl.currentPath); os.IsNotExist(err) {
		return fmt.Errorf("路径不存在: %s", fl.currentPath)
	}

	// 遍历目录查找日志文件
	err := filepath.Walk(fl.currentPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 跳过目录
		if info.IsDir() {
			return nil
		}

		// 检查是否为日志文件（根据扩展名）
		if fl.isLogFile(path) {
			fl.files = append(fl.files, path)

			// 获取相对路径用于显示
			relPath, _ := filepath.Rel(fl.currentPath, path)
			if relPath == "" {
				relPath = filepath.Base(path)
			}

			// 格式化文件大小
			sizeStr := fl.formatFileSize(info.Size())

			// 添加到列表，使用图标和颜色
			icon := fl.getFileIcon(path)
			mainText := fmt.Sprintf("%s %s", icon, relPath)
			secondaryText := fmt.Sprintf("大小: %s", sizeStr)

			fl.AddItem(mainText, secondaryText, 0, nil)
		}

		return nil
	})

	if err != nil {
		return fmt.Errorf("加载文件失败: %v", err)
	}

	if len(fl.files) == 0 {
		fl.AddItem("(无日志文件)", "在当前目录未找到日志文件", 0, nil)
	}

	return nil
}

// isLogFile 判断是否为日志文件
func (fl *FileList) isLogFile(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	logExts := []string{".log", ".txt", ".out", ".err"}

	for _, logExt := range logExts {
		if ext == logExt {
			return true
		}
	}

	// 检查文件名是否包含log关键字
	baseName := strings.ToLower(filepath.Base(filename))
	return strings.Contains(baseName, "log")
}

// SetOnSelect 设置文件选择回调
func (fl *FileList) SetOnSelect(callback func(string)) {
	fl.onSelect = callback
}

// GetCurrentFile 获取当前选中的文件路径
func (fl *FileList) GetCurrentFile() string {
	index := fl.GetCurrentItem()
	if index >= 0 && index < len(fl.files) {
		return fl.files[index]
	}
	return ""
}

// Refresh 刷新文件列表
func (fl *FileList) Refresh() error {
	return fl.loadFiles()
}

// formatFileSize 格式化文件大小
func (fl *FileList) formatFileSize(size int64) string {
	const unit = 1024
	if size < unit {
		return fmt.Sprintf("%d B", size)
	}
	div, exp := int64(unit), 0
	for n := size / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(size)/float64(div), "KMGTPE"[exp])
}

// getFileIcon 获取文件图标
func (fl *FileList) getFileIcon(filename string) string {
	ext := strings.ToLower(filepath.Ext(filename))
	baseName := strings.ToLower(filepath.Base(filename))

	// 根据文件类型返回不同图标
	switch ext {
	case ".log":
		if strings.Contains(baseName, "error") {
			return "🔴" // 错误日志
		} else if strings.Contains(baseName, "access") {
			return "🌐" // 访问日志
		}
		return "📄" // 普通日志
	case ".err":
		return "❌" // 错误文件
	case ".out":
		return "📤" // 输出文件
	case ".txt":
		return "📝" // 文本文件
	default:
		if strings.Contains(baseName, "log") {
			return "📋" // 包含log的文件
		}
		return "📄" // 默认文件
	}
}

// SetSelectedFile 设置选中的文件
func (fl *FileList) SetSelectedFile(filename string) {
	for i, file := range fl.files {
		if file == filename {
			fl.selectedIndex = i
			fl.SetCurrentItem(i)
			break
		}
	}
}
