🎯 项目目标
开发一个终端下运行的、支持多文件查看、关键词高亮、实时 tail 的日志查看工具，便于开发者调试和排查问题，解决传统 vim/tail 不友好、不支持高亮、缺乏搜索交互等问题。

📦 功能规划
MVP（最小可用版本）
模块	功能描述
📁 日志文件列表	左侧列出支持监听的所有日志文件，可用方向键切换
📄 实时日志展示	中间窗口展示当前日志内容，自动滚动
🔍 搜索功能	输入关键词自动高亮匹配行，支持上下切换匹配项
🎨 级别高亮	日志级别（INFO/WARN/ERROR）自动着色
⏸️ 滚动控制	支持暂停滚动（防止刷屏查看历史）
⌨️ 快捷键支持	q=退出，s=搜索，p=暂停，↑↓ 切换文件

可选扩展功能（阶段二）
功能	描述
🔑 文件夹监控	自动监听新日志文件
🔒 简单密码保护	防止他人使用
⏳ 时间范围筛选	查看某时间段的日志
📄 历史搜索记录	快速切换已搜索关键词
🧪 过滤器配置	仅显示包含关键词的行（vs 高亮）

🧱 技术方案
核心组件
模块	技术
TUI 框架	tview（UI 构建）
文件监听	hpcloud/tail（tail 文件）
并发处理	goroutine + channel
日志高亮	ANSI 颜色 + 正则匹配
快捷键绑定	tview.InputCapture

目录结构建议
csharp
复制
编辑
logtui/
├── cmd/                # 命令入口
│   └── main.go
├── internal/
│   ├── ui/             # tview 相关 UI 逻辑
│   ├── tailer/         # 日志 tail 与 channel 管理
│   ├── search/         # 搜索与高亮模块
│   └── utils/
├── assets/             # 可选：配置文件、颜色模板等
├── go.mod
├── README.md
└── LICENSE
🚀 开发计划（7~10 天）
天数	任务描述
Day 1	项目初始化 + 搭建主界面 UI 结构（左右栏 +主窗口）
Day 2	集成 tail 文件监听，日志实时输出
Day 3	增加日志滚动展示逻辑（自动+暂停）
Day 4	实现文件切换 + 日志级别颜色高亮
Day 5	实现搜索关键词功能，匹配项高亮
Day 6	添加快捷键提示栏 + 重构组件划分
Day 7	添加配置管理、README 文档、构建打包
Day 8~10	扩展功能（历史搜索、目录监听）、测试与优化

📄 README 初始内容建议
markdown
复制
编辑
# LogTUI – 终端下的日志可视化工具

LogTUI 是一个基于 Go 编写的终端日志查看器，支持：

- 多日志文件切换
- 实时日志 tail 输出
- 关键词搜索与高亮
- 日志级别着色（INFO/WARN/ERROR）
- 快捷键控制（搜索、暂停滚动、退出）

## 使用方法

```bash
go build -o logtui
./logtui -path ./logs/
快捷键
↑↓ 文件切换

s 搜索关键词

p 暂停滚动

q 退出程序